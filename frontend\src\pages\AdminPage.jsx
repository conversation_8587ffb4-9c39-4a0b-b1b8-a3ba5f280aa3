import React, { useState } from 'react';

const AdminPage = () => {
  const [form, setForm] = useState({ name: '', price: '', quantity: '', description: '', image: '' });
  const [message, setMessage] = useState('');

  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async e => {
    e.preventDefault();
    setMessage('');
    const res = await fetch('http://localhost:5000/api/products', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...form, price: Number(form.price), quantity: Number(form.quantity) })
    });
    if (res.ok) {
      setMessage('Product added!');
      setForm({ name: '', price: '', quantity: '', description: '', image: '' });
    } else {
      setMessage('Error adding product');
    }
  };

  return (
    <section style={{ maxWidth: 400, margin: '2rem auto', padding: '2rem', border: '1px solid #ddd', borderRadius: 8 }}>
      <h2>Add Product</h2>
      <form onSubmit={handleSubmit}>
        <input name="name" value={form.name} onChange={handleChange} placeholder="Name" required style={{ width: '100%', marginBottom: 8 }} />
        <input name="price" value={form.price} onChange={handleChange} placeholder="Price" type="number" required style={{ width: '100%', marginBottom: 8 }} />
        <input name="quantity" value={form.quantity} onChange={handleChange} placeholder="Quantity" type="number" required style={{ width: '100%', marginBottom: 8 }} />
        <input name="image" value={form.image} onChange={handleChange} placeholder="Image URL" style={{ width: '100%', marginBottom: 8 }} />
        <textarea name="description" value={form.description} onChange={handleChange} placeholder="Description" style={{ width: '100%', marginBottom: 8 }} />
        <button type="submit">Add Product</button>
      </form>
      {message && <p>{message}</p>}
    </section>
  );
};

export default AdminPage;
