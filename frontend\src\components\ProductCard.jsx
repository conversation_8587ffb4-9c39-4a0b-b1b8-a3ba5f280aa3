import React from 'react';

const ProductCard = ({ product }) => (
  <div style={{ border: '1px solid #ddd', borderRadius: '8px', padding: '1rem', margin: '1rem', width: '220px' }}>
    {product.image && <img src={product.image} alt={product.name} style={{ width: '100%', height: '120px', objectFit: 'cover', borderRadius: '4px' }} />}
    <h3>{product.name}</h3>
    <p>{product.description}</p>
    <p><b>Price:</b> ${product.price}</p>
    <p><b>Quantity:</b> {product.quantity}</p>
  </div>
);

export default ProductCard;
