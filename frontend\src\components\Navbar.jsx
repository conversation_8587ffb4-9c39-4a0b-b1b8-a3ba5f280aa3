import React from 'react';
import { Link } from 'react-router-dom';

const Navbar = () => (
  <nav style={{ padding: '1rem', display: 'flex', alignItems: 'center' }}>
    <button style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      width: '40px',
      height: '40px',
      background: 'none',
      border: 'none',
      cursor: 'pointer',
      marginRight: '1rem',
    }}>
      <span style={{ height: '4px', background: '#333', margin: '3px 0', borderRadius: '2px' }}></span>
      <span style={{ height: '4px', background: '#333', margin: '3px 0', borderRadius: '2px' }}></span>
      <span style={{ height: '4px', background: '#333', margin: '3px 0', borderRadius: '2px' }}></span>
    </button>
    <Link to="/" style={{ marginRight: '1rem', textDecoration: 'none', color: '#333' }}>Home</Link>
    <Link to="/admin" style={{ textDecoration: 'none', color: '#333' }}>Admin</Link>
  </nav>
);

export default Navbar;
