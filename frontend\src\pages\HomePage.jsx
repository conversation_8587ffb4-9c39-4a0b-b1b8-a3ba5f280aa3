import React, { useEffect, useState } from 'react';
import ProductCard from '../components/ProductCard.jsx';

const HomePage = () => {
  const [products, setProducts] = useState([]);

  useEffect(() => {
    fetch('http://localhost:5000/api/products')
      .then(res => res.json())
      .then(data => setProducts(data));
  }, []);

  return (
    <main style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', marginTop: '2rem' }}>
      {products.map(product => (
        <ProductCard key={product._id} product={product} />
      ))}
    </main>
  );
};

export default HomePage;
